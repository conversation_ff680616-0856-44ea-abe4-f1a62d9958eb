#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转图片工具改进演示
展示预览界面的改进功能
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

def show_improvements():
    """显示改进内容"""
    improvements = """
🎉 预览界面改进完成！

✅ 已修复的问题：
• 鼠标滚轮无法滚动 → 现在支持鼠标滚轮滚动
• 预览尺寸不合理 → 图片自动适应窗口大小
• 无法铺满预览界面 → 智能居中显示，充分利用空间

🚀 新增功能：
• 多种滚动方式：
  - 鼠标滚轮滚动
  - 键盘方向键控制
  - Page Up/Down 快速翻页
  - 传统滚动条拖拽

• 智能布局：
  - 图片自动适应窗口大小
  - 保持原始比例不变形
  - 居中显示更美观
  - 页面间清晰分隔

• 用户体验提升：
  - 显示页码和尺寸信息
  - 预览质量优化
  - 响应式界面设计

🎯 使用提示：
1. 选择PDF文件后点击"预览页面"
2. 使用鼠标滚轮或键盘方向键浏览
3. 窗口大小改变时图片会自动调整
4. 预览完成后点击"转换并保存"

现在就试试新的预览功能吧！
    """
    
    root = tk.Tk()
    root.title("预览界面改进说明")
    root.geometry("600x500")
    
    # 创建文本框显示改进内容
    text_frame = ttk.Frame(root, padding="10")
    text_frame.pack(fill=tk.BOTH, expand=True)
    
    text_widget = tk.Text(text_frame, wrap=tk.WORD, font=("Microsoft YaHei", 10))
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    
    # 添加滚动条
    scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    # 插入改进内容
    text_widget.insert(tk.END, improvements)
    text_widget.configure(state=tk.DISABLED)
    
    # 按钮框架
    button_frame = ttk.Frame(root, padding="10")
    button_frame.pack(fill=tk.X)
    
    def launch_app():
        """启动主应用程序"""
        root.destroy()
        try:
            os.system("python pdf_to_image_gui.py")
        except Exception as e:
            messagebox.showerror("错误", f"启动应用程序失败: {e}")
    
    def show_usage():
        """显示使用说明"""
        try:
            os.startfile("使用说明.md")  # Windows
        except:
            try:
                os.system("open 使用说明.md")  # macOS
            except:
                os.system("xdg-open 使用说明.md")  # Linux
    
    ttk.Button(button_frame, text="启动应用程序", command=launch_app).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="查看使用说明", command=show_usage).pack(side=tk.LEFT, padx=5)
    ttk.Button(button_frame, text="关闭", command=root.destroy).pack(side=tk.RIGHT, padx=5)
    
    root.mainloop()

if __name__ == "__main__":
    show_improvements()
