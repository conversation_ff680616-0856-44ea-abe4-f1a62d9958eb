#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF转图片工具测试脚本
用于验证应用程序的基本功能
"""

import os
import sys
import tempfile
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter

def create_test_pdf():
    """创建一个测试用的PDF文件"""
    try:
        # 创建临时PDF文件
        temp_dir = tempfile.gettempdir()
        pdf_path = os.path.join(temp_dir, "test_document.pdf")
        
        # 使用reportlab创建PDF
        c = canvas.Canvas(pdf_path, pagesize=letter)
        
        # 创建15页内容
        for page_num in range(1, 16):
            c.drawString(100, 750, f"这是第 {page_num} 页")
            c.drawString(100, 700, f"Page {page_num} of Test Document")
            c.drawString(100, 650, "这是一个测试PDF文件")
            c.drawString(100, 600, "用于测试PDF转图片工具")
            
            # 添加一些图形
            c.rect(100, 400, 200, 100)
            c.drawString(120, 440, f"矩形 - 第{page_num}页")
            
            c.circle(300, 450, 50)
            c.drawString(280, 445, f"圆形")
            
            c.showPage()
        
        c.save()
        print(f"测试PDF文件已创建: {pdf_path}")
        return pdf_path
        
    except ImportError:
        print("警告: 未安装reportlab库，无法创建测试PDF")
        print("请运行: pip install reportlab")
        return None
    except Exception as e:
        print(f"创建测试PDF时出错: {e}")
        return None

def main():
    """主函数"""
    print("PDF转图片工具测试")
    print("=" * 40)
    
    # 检查依赖
    try:
        import fitz
        import PIL
        print("✓ PyMuPDF 已安装")
        print("✓ Pillow 已安装")
    except ImportError as e:
        print(f"✗ 缺少依赖: {e}")
        print("请运行: pip install -r requirements.txt")
        return
    
    # 创建测试PDF
    test_pdf = create_test_pdf()
    if test_pdf:
        print(f"✓ 测试PDF已创建")
        print(f"  路径: {test_pdf}")
        print("\n现在可以使用GUI工具测试以下功能:")
        print("1. 加载这个测试PDF文件")
        print("2. 设置页码为12-13页")
        print("3. 预览页面内容")
        print("4. 转换并保存图片")
    
    print("\n启动GUI应用程序...")
    
    # 启动GUI应用
    try:
        from pdf_to_image_gui import PDFToImageGUI
        import tkinter as tk
        
        root = tk.Tk()
        app = PDFToImageGUI(root)
        
        # 如果有测试PDF，自动加载
        if test_pdf:
            app.pdf_path.set(test_pdf)
            app.load_pdf()
        
        root.protocol("WM_DELETE_WINDOW", app.on_closing)
        root.mainloop()
        
    except Exception as e:
        print(f"启动GUI时出错: {e}")

if __name__ == "__main__":
    main()
