# PDF页面转图片工具 - 使用说明

## 功能概述

这是一个简单易用的PDF页面转图片工具，具有以下特性：

- ✅ 简洁的图形用户界面
- ✅ 选择PDF文件
- ✅ 自动转换指定页面为图片（默认第12-13页）
- ✅ 可调节图片清晰度（7个级别）
- ✅ 实时预览要导出的页面
- ✅ 手动修改导出图片的页码
- ✅ 图片自动保存在PDF同目录下

## 安装和运行

### 1. 安装依赖
```bash
pip install -r requirements.txt
```

### 2. 运行程序
```bash
python pdf_to_image_gui.py
```

## 使用步骤

### 第一步：选择PDF文件
1. 点击"浏览"按钮
2. 在文件对话框中选择要处理的PDF文件
3. 程序会自动加载PDF并显示总页数

### 第二步：设置页码范围
1. 在"页码设置"区域调整起始页和结束页
2. 默认设置为第12页到第13页
3. 可以使用数字输入框或点击上下箭头调整
4. 状态栏会实时显示将要转换的页面信息

### 第三步：选择图片清晰度
在"图片清晰度"下拉菜单中选择合适的清晰度：
- **非常模糊** (0.5x) - 文件最小，速度最快
- **模糊** (1.0x) - 较小文件
- **普通** (1.5x) - 平衡选择
- **清晰** (2.0x) - 推荐设置
- **高清** (2.5x) - 默认设置，质量较好
- **超高清** (3.0x) - 高质量
- **非常清晰** (4.0x) - 最高质量，文件较大

### 第四步：预览页面（可选）
1. 点击"预览页面"按钮
2. 在预览区域查看将要转换的页面
3. 支持多种滚动方式：
   - **鼠标滚轮**：上下滚动预览
   - **键盘方向键**：上下箭头精确滚动
   - **Page Up/Down**：快速翻页
   - **滚动条**：拖拽滚动
4. 预览图片会自动适应窗口大小
5. 确认页面内容正确

### 第五步：转换并保存
1. 点击"转换并保存"按钮
2. 程序会在后台处理，状态栏显示进度
3. 转换完成后会弹出成功提示
4. 图片文件保存在PDF文件的同一目录下

## 输出文件

- **文件格式**: PNG
- **文件命名**: `原PDF文件名_page_页码.png`
- **保存位置**: PDF文件所在目录
- **例如**: `document_page_12.png`, `document_page_13.png`

## 预览界面特性

### 智能尺寸调整
- 预览图片会根据窗口大小自动调整
- 保持原始比例，确保内容不变形
- 居中显示，美观整洁

### 多种滚动方式
- **鼠标滚轮**：最常用的滚动方式
- **键盘控制**：方向键精确控制，Page Up/Down快速翻页
- **滚动条**：传统的拖拽滚动方式

### 详细信息显示
- 显示页码标题
- 显示图片尺寸信息
- 页面间有清晰的分隔线

## 其他功能

### 打开输出文件夹
点击"打开输出文件夹"按钮可以直接打开保存图片的文件夹。

### 关于信息
点击"关于"按钮查看程序版本和功能信息。

## 注意事项

1. **页码范围**: 确保页码在PDF总页数范围内
2. **文件权限**: 确保对PDF文件和输出目录有读写权限
3. **内存使用**: 高清晰度设置会消耗更多内存和时间
4. **文件覆盖**: 如果输出文件已存在，会被覆盖

## 故障排除

### 常见问题

**Q: 程序无法启动**
A: 检查是否已安装所有依赖库：`pip install -r requirements.txt`

**Q: 无法加载PDF文件**
A: 确认PDF文件没有密码保护，且文件路径中没有特殊字符

**Q: 预览显示空白**
A: 可能是PDF页面为空或包含特殊内容，尝试其他页面

**Q: 转换速度慢**
A: 降低清晰度设置或减少转换页面数量

**Q: 输出图片质量不佳**
A: 提高清晰度设置，推荐使用"高清"或"超高清"

## 技术信息

- **开发语言**: Python 3.x
- **GUI框架**: tkinter
- **PDF处理**: PyMuPDF (fitz)
- **图像处理**: Pillow (PIL)
- **支持系统**: Windows, macOS, Linux
