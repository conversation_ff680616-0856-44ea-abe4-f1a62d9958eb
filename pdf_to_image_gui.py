import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import fitz  # PyMuPDF
from PIL import Image, ImageTk
import os
import threading
import io


class PDFToImageGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF页面转图片工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 变量初始化
        self.pdf_path = tk.StringVar()
        self.start_page = tk.IntVar(value=12)
        self.end_page = tk.IntVar(value=13)
        self.quality_var = tk.StringVar(value="高清")
        self.pdf_document = None
        self.preview_image = None
        
        # 质量设置映射
        self.quality_settings = {
            "非常模糊": 0.5,
            "模糊": 1.0,
            "普通": 1.5,
            "清晰": 2.0,
            "高清": 2.5,
            "超高清": 3.0,
            "非常清晰": 4.0
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 文件选择区域
        ttk.Label(main_frame, text="PDF文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.pdf_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_file).grid(row=0, column=2, padx=5, pady=5)
        
        # 页码设置区域
        page_frame = ttk.LabelFrame(main_frame, text="页码设置", padding="5")
        page_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        page_frame.columnconfigure(1, weight=1)
        page_frame.columnconfigure(3, weight=1)
        
        ttk.Label(page_frame, text="起始页:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.start_spinbox = ttk.Spinbox(page_frame, from_=1, to=9999, textvariable=self.start_page, width=10, command=self.validate_pages)
        self.start_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5)

        ttk.Label(page_frame, text="结束页:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.end_spinbox = ttk.Spinbox(page_frame, from_=1, to=9999, textvariable=self.end_page, width=10, command=self.validate_pages)
        self.end_spinbox.grid(row=0, column=3, sticky=tk.W, padx=5)
        
        # 清晰度设置
        quality_frame = ttk.LabelFrame(main_frame, text="图片清晰度", padding="5")
        quality_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(quality_frame, text="清晰度:").grid(row=0, column=0, sticky=tk.W, padx=5)
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var, 
                                   values=list(self.quality_settings.keys()), 
                                   state="readonly", width=15)
        quality_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 预览和操作区域
        preview_frame = ttk.LabelFrame(main_frame, text="预览", padding="5")
        preview_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, bg="white", height=300)
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 滚动条
        v_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_canvas.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set)

        h_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.preview_canvas.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.preview_canvas.configure(xscrollcommand=h_scrollbar.set)

        # 绑定鼠标滚轮事件
        self.preview_canvas.bind("<MouseWheel>", self.on_mousewheel)
        self.preview_canvas.bind("<Button-4>", self.on_mousewheel)  # Linux
        self.preview_canvas.bind("<Button-5>", self.on_mousewheel)  # Linux

        # 绑定画布大小变化事件
        self.preview_canvas.bind("<Configure>", self.on_canvas_configure)

        # 让画布可以获得焦点以响应键盘事件
        self.preview_canvas.focus_set()

        # 绑定键盘事件
        self.preview_canvas.bind("<Up>", lambda e: self.preview_canvas.yview_scroll(-1, "units"))
        self.preview_canvas.bind("<Down>", lambda e: self.preview_canvas.yview_scroll(1, "units"))
        self.preview_canvas.bind("<Prior>", lambda e: self.preview_canvas.yview_scroll(-10, "units"))  # Page Up
        self.preview_canvas.bind("<Next>", lambda e: self.preview_canvas.yview_scroll(10, "units"))   # Page Down
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)

        self.preview_btn = ttk.Button(button_frame, text="预览页面", command=self.preview_pages)
        self.preview_btn.pack(side=tk.LEFT, padx=5)

        self.convert_btn = ttk.Button(button_frame, text="转换并保存", command=self.convert_and_save)
        self.convert_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="打开输出文件夹", command=self.open_output_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.LEFT, padx=5)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    def browse_file(self):
        """浏览并选择PDF文件"""
        filename = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.pdf_path.set(filename)
            self.load_pdf()
    
    def load_pdf(self):
        """加载PDF文档"""
        try:
            if self.pdf_document:
                self.pdf_document.close()
            
            self.pdf_document = fitz.open(self.pdf_path.get())
            total_pages = len(self.pdf_document)
            self.status_var.set(f"PDF已加载，共{total_pages}页")
            
            # 更新页码范围
            if self.end_page.get() > total_pages:
                self.end_page.set(total_pages)
            if self.start_page.get() > total_pages:
                self.start_page.set(1)

            # 更新spinbox范围
            self.start_spinbox.config(to=total_pages)
            self.end_spinbox.config(to=total_pages)

        except Exception as e:
            messagebox.showerror("错误", f"无法加载PDF文件: {str(e)}")
            self.status_var.set("加载失败")

    def validate_pages(self):
        """验证页码设置"""
        if not self.pdf_document:
            return

        try:
            start = self.start_page.get()
            end = self.end_page.get()
            total_pages = len(self.pdf_document)

            if start > end:
                self.status_var.set("警告: 起始页不能大于结束页")
            elif start < 1 or end > total_pages:
                self.status_var.set(f"警告: 页码范围应在1-{total_pages}之间")
            else:
                self.status_var.set(f"将转换第{start}页到第{end}页 (共{end-start+1}页)")
        except:
            pass

    def on_mousewheel(self, event):
        """处理鼠标滚轮事件"""
        # Windows和macOS
        if event.delta:
            delta = -1 * (event.delta / 120)
        # Linux
        elif event.num == 4:
            delta = -1
        elif event.num == 5:
            delta = 1
        else:
            delta = 0

        self.preview_canvas.yview_scroll(int(delta), "units")

    def on_canvas_configure(self, event):
        """画布大小变化时的处理"""
        # 更新画布的滚动区域
        bbox = self.preview_canvas.bbox("all")
        if bbox:
            self.preview_canvas.configure(scrollregion=bbox)

    def preview_pages(self):
        """预览指定页面"""
        if not self.pdf_document:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        try:
            start = self.start_page.get()
            end = self.end_page.get()

            if start > end:
                messagebox.showwarning("警告", "起始页不能大于结束页")
                return

            if start < 1 or end > len(self.pdf_document):
                messagebox.showwarning("警告", f"页码范围应在1-{len(self.pdf_document)}之间")
                return

            self.status_var.set("正在生成预览...")
            self.root.update()

            # 在新线程中生成预览
            threading.Thread(target=self._generate_preview, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"预览失败: {str(e)}")
            self.status_var.set("预览失败")

    def _generate_preview(self):
        """在后台线程中生成预览"""
        try:
            start = self.start_page.get()
            end = self.end_page.get()
            quality = min(1.5, self.quality_settings[self.quality_var.get()])  # 预览用较低质量

            # 清除之前的预览
            self.root.after(0, self.preview_canvas.delete, "all")
            self.root.after(0, self._clear_preview_images)

            # 获取画布当前尺寸
            canvas_width = self.preview_canvas.winfo_width()
            if canvas_width <= 1:  # 如果画布还没有渲染，使用默认值
                canvas_width = 600

            y_offset = 10
            max_width = 0

            for page_num in range(start - 1, end):
                page = self.pdf_document[page_num]

                # 渲染页面为图像
                mat = fitz.Matrix(quality, quality)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("ppm")

                # 转换为PIL图像
                pil_image = Image.open(io.BytesIO(img_data))

                # 计算适合画布的预览尺寸
                original_width = pil_image.width
                original_height = pil_image.height

                # 预留边距和滚动条空间
                available_width = canvas_width - 40
                if available_width < 200:
                    available_width = 400

                # 按比例缩放
                scale_factor = min(available_width / original_width, 800 / original_height)
                preview_width = int(original_width * scale_factor)
                preview_height = int(original_height * scale_factor)

                # 确保最小尺寸
                if preview_width < 200:
                    preview_width = 200
                    preview_height = int(200 * original_height / original_width)

                pil_image = pil_image.resize((preview_width, preview_height), Image.Resampling.LANCZOS)

                # 转换为tkinter图像
                tk_image = ImageTk.PhotoImage(pil_image)

                # 在主线程中更新UI
                self.root.after(0, self._update_preview, tk_image, y_offset, page_num + 1, preview_width)

                y_offset += preview_height + 30  # 增加页面间距
                max_width = max(max_width, preview_width + 20)

            # 更新滚动区域
            self.root.after(0, self._update_scroll_region, max_width, y_offset)
            self.root.after(0, self.status_var.set, "预览完成")

        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"生成预览失败: {str(e)}")
            self.root.after(0, self.status_var.set, "预览失败")

    def _clear_preview_images(self):
        """清除预览图像引用"""
        if hasattr(self, 'preview_images'):
            self.preview_images.clear()
        else:
            self.preview_images = []

    def _update_scroll_region(self, max_width, y_offset):
        """更新滚动区域"""
        self.preview_canvas.configure(scrollregion=(0, 0, max_width, y_offset))
        # 滚动到顶部
        self.preview_canvas.yview_moveto(0)

    def _update_preview(self, tk_image, y_offset, page_num, image_width):
        """在主线程中更新预览"""
        # 保存图像引用防止被垃圾回收
        if not hasattr(self, 'preview_images'):
            self.preview_images = []
        self.preview_images.append(tk_image)

        # 计算居中位置
        canvas_width = self.preview_canvas.winfo_width()
        if canvas_width <= 1:
            canvas_width = 600

        x_center = max(10, (canvas_width - image_width) // 2)

        # 添加页面标签（居中）
        label_x = max(10, canvas_width // 2)
        self.preview_canvas.create_text(label_x, y_offset, text=f"第{page_num}页",
                                      anchor="n", font=("Arial", 14, "bold"),
                                      fill="#2c3e50")

        # 添加分隔线
        line_y = y_offset + 20
        self.preview_canvas.create_line(10, line_y, canvas_width - 10, line_y,
                                      fill="#bdc3c7", width=1)

        # 添加图像（居中）
        self.preview_canvas.create_image(x_center, y_offset + 25, image=tk_image, anchor="nw")

        # 添加页面信息
        info_text = f"尺寸: {tk_image.width()} × {tk_image.height()}"
        self.preview_canvas.create_text(x_center, y_offset + tk_image.height() + 30,
                                      text=info_text, anchor="nw",
                                      font=("Arial", 10), fill="#7f8c8d")

    def convert_and_save(self):
        """转换页面为图片并保存"""
        if not self.pdf_document:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        try:
            start = self.start_page.get()
            end = self.end_page.get()

            if start > end:
                messagebox.showwarning("警告", "起始页不能大于结束页")
                return

            if start < 1 or end > len(self.pdf_document):
                messagebox.showwarning("警告", f"页码范围应在1-{len(self.pdf_document)}之间")
                return

            # 获取PDF文件目录
            pdf_dir = os.path.dirname(self.pdf_path.get())
            pdf_name = os.path.splitext(os.path.basename(self.pdf_path.get()))[0]

            self.status_var.set("正在转换并保存...")
            self.root.update()

            # 在新线程中执行转换
            threading.Thread(target=self._convert_and_save_thread,
                           args=(start, end, pdf_dir, pdf_name), daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"转换失败: {str(e)}")
            self.status_var.set("转换失败")

    def _convert_and_save_thread(self, start, end, pdf_dir, pdf_name):
        """在后台线程中执行转换和保存"""
        try:
            quality = self.quality_settings[self.quality_var.get()]
            saved_files = []

            for page_num in range(start - 1, end):
                page = self.pdf_document[page_num]

                # 渲染页面为高质量图像
                mat = fitz.Matrix(quality, quality)
                pix = page.get_pixmap(matrix=mat)

                # 生成文件名
                filename = f"{pdf_name}_page_{page_num + 1}.png"
                filepath = os.path.join(pdf_dir, filename)

                # 保存图像
                pix.save(filepath)
                saved_files.append(filepath)

                # 更新进度
                progress = f"正在保存第{page_num + 1}页..."
                self.root.after(0, self.status_var.set, progress)

            # 转换完成
            success_msg = f"成功保存{len(saved_files)}个文件到:\n{pdf_dir}"
            self.root.after(0, messagebox.showinfo, "完成", success_msg)
            self.root.after(0, self.status_var.set, f"转换完成，已保存{len(saved_files)}个文件")

        except Exception as e:
            error_msg = f"转换过程中出错: {str(e)}"
            self.root.after(0, messagebox.showerror, "错误", error_msg)
            self.root.after(0, self.status_var.set, "转换失败")

    def open_output_folder(self):
        """打开输出文件夹"""
        if not self.pdf_path.get():
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        pdf_dir = os.path.dirname(self.pdf_path.get())
        try:
            os.startfile(pdf_dir)  # Windows
        except AttributeError:
            try:
                os.system(f'open "{pdf_dir}"')  # macOS
            except:
                os.system(f'xdg-open "{pdf_dir}"')  # Linux

    def show_about(self):
        """显示关于对话框"""
        about_text = """PDF页面转图片工具 v1.0

功能特性:
• 选择PDF文件并转换指定页面为图片
• 支持多种清晰度设置
• 实时预览要导出的页面
• 图片自动保存在PDF同目录下

开发: Python + tkinter + PyMuPDF
作者: AI Assistant"""

        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.pdf_document:
            self.pdf_document.close()
        self.root.destroy()


if __name__ == "__main__":
    root = tk.Tk()
    app = PDFToImageGUI(root)

    # 设置关闭事件处理
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    root.mainloop()
