import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import fitz  # PyMuPDF
from PIL import Image, ImageTk
import os
import threading
import io


class PDFToImageGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("PDF页面转图片工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 变量初始化
        self.pdf_path = tk.StringVar()
        self.start_page = tk.IntVar(value=12)
        self.end_page = tk.IntVar(value=13)
        self.quality_var = tk.StringVar(value="高清")
        self.pdf_document = None
        self.preview_image = None
        
        # 质量设置映射
        self.quality_settings = {
            "非常模糊": 0.5,
            "模糊": 1.0,
            "普通": 1.5,
            "清晰": 2.0,
            "高清": 2.5,
            "超高清": 3.0,
            "非常清晰": 4.0
        }
        
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # 文件选择区域
        ttk.Label(main_frame, text="PDF文件:").grid(row=0, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.pdf_path, width=50).grid(row=0, column=1, sticky=(tk.W, tk.E), padx=5, pady=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_file).grid(row=0, column=2, padx=5, pady=5)
        
        # 页码设置区域
        page_frame = ttk.LabelFrame(main_frame, text="页码设置", padding="5")
        page_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        page_frame.columnconfigure(1, weight=1)
        page_frame.columnconfigure(3, weight=1)
        
        ttk.Label(page_frame, text="起始页:").grid(row=0, column=0, sticky=tk.W, padx=5)
        self.start_spinbox = ttk.Spinbox(page_frame, from_=1, to=9999, textvariable=self.start_page, width=10, command=self.validate_pages)
        self.start_spinbox.grid(row=0, column=1, sticky=tk.W, padx=5)

        ttk.Label(page_frame, text="结束页:").grid(row=0, column=2, sticky=tk.W, padx=5)
        self.end_spinbox = ttk.Spinbox(page_frame, from_=1, to=9999, textvariable=self.end_page, width=10, command=self.validate_pages)
        self.end_spinbox.grid(row=0, column=3, sticky=tk.W, padx=5)
        
        # 清晰度设置
        quality_frame = ttk.LabelFrame(main_frame, text="图片清晰度", padding="5")
        quality_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Label(quality_frame, text="清晰度:").grid(row=0, column=0, sticky=tk.W, padx=5)
        quality_combo = ttk.Combobox(quality_frame, textvariable=self.quality_var, 
                                   values=list(self.quality_settings.keys()), 
                                   state="readonly", width=15)
        quality_combo.grid(row=0, column=1, sticky=tk.W, padx=5)
        
        # 预览和操作区域
        preview_frame = ttk.LabelFrame(main_frame, text="预览", padding="5")
        preview_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        preview_frame.columnconfigure(0, weight=1)
        preview_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # 预览画布
        self.preview_canvas = tk.Canvas(preview_frame, bg="white", height=300)
        self.preview_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        v_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.VERTICAL, command=self.preview_canvas.yview)
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.preview_canvas.configure(yscrollcommand=v_scrollbar.set)
        
        h_scrollbar = ttk.Scrollbar(preview_frame, orient=tk.HORIZONTAL, command=self.preview_canvas.xview)
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        self.preview_canvas.configure(xscrollcommand=h_scrollbar.set)
        
        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=3, pady=10)

        self.preview_btn = ttk.Button(button_frame, text="预览页面", command=self.preview_pages)
        self.preview_btn.pack(side=tk.LEFT, padx=5)

        self.convert_btn = ttk.Button(button_frame, text="转换并保存", command=self.convert_and_save)
        self.convert_btn.pack(side=tk.LEFT, padx=5)

        ttk.Button(button_frame, text="打开输出文件夹", command=self.open_output_folder).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="关于", command=self.show_about).pack(side=tk.LEFT, padx=5)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN)
        status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
    
    def browse_file(self):
        """浏览并选择PDF文件"""
        filename = filedialog.askopenfilename(
            title="选择PDF文件",
            filetypes=[("PDF files", "*.pdf"), ("All files", "*.*")]
        )
        if filename:
            self.pdf_path.set(filename)
            self.load_pdf()
    
    def load_pdf(self):
        """加载PDF文档"""
        try:
            if self.pdf_document:
                self.pdf_document.close()
            
            self.pdf_document = fitz.open(self.pdf_path.get())
            total_pages = len(self.pdf_document)
            self.status_var.set(f"PDF已加载，共{total_pages}页")
            
            # 更新页码范围
            if self.end_page.get() > total_pages:
                self.end_page.set(total_pages)
            if self.start_page.get() > total_pages:
                self.start_page.set(1)

            # 更新spinbox范围
            self.start_spinbox.config(to=total_pages)
            self.end_spinbox.config(to=total_pages)

        except Exception as e:
            messagebox.showerror("错误", f"无法加载PDF文件: {str(e)}")
            self.status_var.set("加载失败")

    def validate_pages(self):
        """验证页码设置"""
        if not self.pdf_document:
            return

        try:
            start = self.start_page.get()
            end = self.end_page.get()
            total_pages = len(self.pdf_document)

            if start > end:
                self.status_var.set("警告: 起始页不能大于结束页")
            elif start < 1 or end > total_pages:
                self.status_var.set(f"警告: 页码范围应在1-{total_pages}之间")
            else:
                self.status_var.set(f"将转换第{start}页到第{end}页 (共{end-start+1}页)")
        except:
            pass

    def preview_pages(self):
        """预览指定页面"""
        if not self.pdf_document:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        try:
            start = self.start_page.get()
            end = self.end_page.get()

            if start > end:
                messagebox.showwarning("警告", "起始页不能大于结束页")
                return

            if start < 1 or end > len(self.pdf_document):
                messagebox.showwarning("警告", f"页码范围应在1-{len(self.pdf_document)}之间")
                return

            self.status_var.set("正在生成预览...")
            self.root.update()

            # 在新线程中生成预览
            threading.Thread(target=self._generate_preview, daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"预览失败: {str(e)}")
            self.status_var.set("预览失败")

    def _generate_preview(self):
        """在后台线程中生成预览"""
        try:
            start = self.start_page.get()
            end = self.end_page.get()
            quality = self.quality_settings[self.quality_var.get()]

            # 清除之前的预览
            self.root.after(0, self.preview_canvas.delete, "all")

            y_offset = 10
            max_width = 0

            for page_num in range(start - 1, end):
                page = self.pdf_document[page_num]

                # 渲染页面为图像
                mat = fitz.Matrix(quality, quality)
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.tobytes("ppm")

                # 转换为PIL图像
                pil_image = Image.open(io.BytesIO(img_data))

                # 调整预览大小
                preview_width = 400
                aspect_ratio = pil_image.height / pil_image.width
                preview_height = int(preview_width * aspect_ratio)

                pil_image = pil_image.resize((preview_width, preview_height), Image.Resampling.LANCZOS)

                # 转换为tkinter图像
                tk_image = ImageTk.PhotoImage(pil_image)

                # 在主线程中更新UI
                self.root.after(0, self._update_preview, tk_image, y_offset, page_num + 1)

                y_offset += preview_height + 20
                max_width = max(max_width, preview_width)

            # 更新滚动区域
            self.root.after(0, self.preview_canvas.configure, {"scrollregion": (0, 0, max_width, y_offset)})
            self.root.after(0, self.status_var.set, "预览完成")

        except Exception as e:
            self.root.after(0, messagebox.showerror, "错误", f"生成预览失败: {str(e)}")
            self.root.after(0, self.status_var.set, "预览失败")

    def _update_preview(self, tk_image, y_offset, page_num):
        """在主线程中更新预览"""
        # 保存图像引用防止被垃圾回收
        if not hasattr(self, 'preview_images'):
            self.preview_images = []
        self.preview_images.append(tk_image)

        # 添加页面标签
        self.preview_canvas.create_text(10, y_offset, text=f"第{page_num}页", anchor="nw", font=("Arial", 12, "bold"))

        # 添加图像
        self.preview_canvas.create_image(10, y_offset + 25, image=tk_image, anchor="nw")

    def convert_and_save(self):
        """转换页面为图片并保存"""
        if not self.pdf_document:
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        try:
            start = self.start_page.get()
            end = self.end_page.get()

            if start > end:
                messagebox.showwarning("警告", "起始页不能大于结束页")
                return

            if start < 1 or end > len(self.pdf_document):
                messagebox.showwarning("警告", f"页码范围应在1-{len(self.pdf_document)}之间")
                return

            # 获取PDF文件目录
            pdf_dir = os.path.dirname(self.pdf_path.get())
            pdf_name = os.path.splitext(os.path.basename(self.pdf_path.get()))[0]

            self.status_var.set("正在转换并保存...")
            self.root.update()

            # 在新线程中执行转换
            threading.Thread(target=self._convert_and_save_thread,
                           args=(start, end, pdf_dir, pdf_name), daemon=True).start()

        except Exception as e:
            messagebox.showerror("错误", f"转换失败: {str(e)}")
            self.status_var.set("转换失败")

    def _convert_and_save_thread(self, start, end, pdf_dir, pdf_name):
        """在后台线程中执行转换和保存"""
        try:
            quality = self.quality_settings[self.quality_var.get()]
            saved_files = []

            for page_num in range(start - 1, end):
                page = self.pdf_document[page_num]

                # 渲染页面为高质量图像
                mat = fitz.Matrix(quality, quality)
                pix = page.get_pixmap(matrix=mat)

                # 生成文件名
                filename = f"{pdf_name}_page_{page_num + 1}.png"
                filepath = os.path.join(pdf_dir, filename)

                # 保存图像
                pix.save(filepath)
                saved_files.append(filepath)

                # 更新进度
                progress = f"正在保存第{page_num + 1}页..."
                self.root.after(0, self.status_var.set, progress)

            # 转换完成
            success_msg = f"成功保存{len(saved_files)}个文件到:\n{pdf_dir}"
            self.root.after(0, messagebox.showinfo, "完成", success_msg)
            self.root.after(0, self.status_var.set, f"转换完成，已保存{len(saved_files)}个文件")

        except Exception as e:
            error_msg = f"转换过程中出错: {str(e)}"
            self.root.after(0, messagebox.showerror, "错误", error_msg)
            self.root.after(0, self.status_var.set, "转换失败")

    def open_output_folder(self):
        """打开输出文件夹"""
        if not self.pdf_path.get():
            messagebox.showwarning("警告", "请先选择PDF文件")
            return

        pdf_dir = os.path.dirname(self.pdf_path.get())
        try:
            os.startfile(pdf_dir)  # Windows
        except AttributeError:
            try:
                os.system(f'open "{pdf_dir}"')  # macOS
            except:
                os.system(f'xdg-open "{pdf_dir}"')  # Linux

    def show_about(self):
        """显示关于对话框"""
        about_text = """PDF页面转图片工具 v1.0

功能特性:
• 选择PDF文件并转换指定页面为图片
• 支持多种清晰度设置
• 实时预览要导出的页面
• 图片自动保存在PDF同目录下

开发: Python + tkinter + PyMuPDF
作者: AI Assistant"""

        messagebox.showinfo("关于", about_text)

    def on_closing(self):
        """程序关闭时的清理工作"""
        if self.pdf_document:
            self.pdf_document.close()
        self.root.destroy()


if __name__ == "__main__":
    root = tk.Tk()
    app = PDFToImageGUI(root)

    # 设置关闭事件处理
    root.protocol("WM_DELETE_WINDOW", app.on_closing)

    root.mainloop()
